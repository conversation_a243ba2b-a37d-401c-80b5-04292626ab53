/**
 * Test user management utilities
 *
 * This file provides utilities for managing test users in e2e tests.
 * Users are created programmatically via Better Auth API to ensure proper integration.
 */

import { db } from '../../../server/utils/drizzle.js';
import { user } from '../../../server/db/schema/auth.js';
import { eq, like } from 'drizzle-orm';
import { auth } from '../../../server/utils/auth.js';

/**
 * Interface for test user data
 */
export interface TestUserData {
	name: string;
	email: string;
	password: string;
}

/**
 * Generates test user data with unique email
 * @param overrides Optional overrides for default values
 * @returns TestUserData object
 */
export function generateTestUserData(overrides: Partial<TestUserData> = {}): TestUserData {
	const timestamp = Date.now();
	return {
		name: 'Test User',
		email: `test-user-${timestamp}@example.com`,
		password: 'TestPassword123!',
		...overrides,
	};
}

/**
 * Creates a test user via Better Auth API
 * @param userData User data to create
 * @returns Promise that resolves when user is created
 */
export async function createTestUser(userData: TestUserData): Promise<void> {
	await auth.api.signUpEmail({
		body: {
			name: userData.name,
			email: userData.email,
			password: userData.password,
		},
	});
}

/**
 * Creates a verified test user
 * @param userData User data to create
 * @returns Promise that resolves when verified user is created
 */
export async function createVerifiedTestUser(userData: TestUserData): Promise<void> {
	// Create the user first
	await createTestUser(userData);

	// Mark as verified by updating the database directly
	// This is a test utility, so direct DB manipulation is acceptable
	await db.update(user).set({ emailVerified: true }).where(eq(user.email, userData.email));
}

/**
 * Deletes a test user from the database
 * @param email Email of the user to delete
 */
export async function deleteTestUser(email: string): Promise<void> {
	try {
		await db.delete(user).where(eq(user.email, email));
		if (process.env.NODE_ENV === 'test' || process.env.NUXT_ENV_TEST === 'true') {
			console.log(`🗑️  Deleted test user: ${email}`);
		}
	} catch (error) {
		// Log the error but don't throw as the user might not exist
		if (process.env.NODE_ENV === 'test' || process.env.NUXT_ENV_TEST === 'true') {
			console.warn(`⚠️  Failed to delete test user ${email}:`, error);
		}
	}
}

/**
 * Deletes multiple test users from the database
 * @param emails Array of emails to delete
 */
export async function deleteTestUsers(emails: string[]): Promise<void> {
	for (const email of emails) {
		await deleteTestUser(email);
	}
}

/**
 * Cleans up all test users (safety cleanup function)
 * This function removes all users with test-related email patterns
 */
export async function cleanupAllTestUsers(): Promise<void> {
	try {
		const testUsers = await db.select({ id: user.id, email: user.email }).from(user).where(like(user.email, '%test%'));

		const exampleUsers = await db.select({ id: user.id, email: user.email }).from(user).where(like(user.email, '%example.com'));

		const allTestUsers = [...testUsers, ...exampleUsers];

		if (allTestUsers.length > 0) {
			// Delete all test users
			await db.delete(user).where(like(user.email, '%test%'));
			await db.delete(user).where(like(user.email, '%example.com'));

			if (process.env.NODE_ENV === 'test' || process.env.NUXT_ENV_TEST === 'true') {
				console.log(`🧹 Cleaned up ${allTestUsers.length} test users`);
			}
		}
	} catch (error) {
		if (process.env.NODE_ENV === 'test' || process.env.NUXT_ENV_TEST === 'true') {
			console.error('❌ Failed to cleanup test users:', error);
		}
		throw error;
	}
}
