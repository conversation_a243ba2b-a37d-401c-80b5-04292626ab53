import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad, signInUser } from '../utils/helpers.js';
import { deleteTestUser, generateTestUserData, createTestUser, createVerifiedTestUser } from '../utils/test-users.js';
import { db } from '../../../server/utils/drizzle.js';
import { user } from '../../../server/db/schema/auth.js';
import { eq } from 'drizzle-orm';

// Test users for different verification scenarios - generate unique users each time
let UNVERIFIED_USER: ReturnType<typeof generateTestUserData>;
let VERIFIED_USER: ReturnType<typeof generateTestUserData>;

test.describe('Verify Page', () => {
	// Setup test users before running tests
	test.beforeAll(async () => {
		// Generate unique test users for this test run
		UNVERIFIED_USER = generateTestUserData({
			name: 'Unverified Test User',
		});

		VERIFIED_USER = generateTestUserData({
			name: 'Verified Test User',
		});

		// Clean up any existing test users
		await deleteTestUser(UNVERIFIED_USER.email);
		await deleteTestUser(VERIFIED_USER.email);

		try {
			// Create unverified user
			await createTestUser(UNVERIFIED_USER);
		} catch (error) {
			// If user already exists, that's fine for testing
			if (!(error instanceof Error) || !error.message?.includes('already exists')) {
				console.error('Failed to create unverified test user:', error);
				throw error;
			}
		}

		try {
			// Create verified user
			await createVerifiedTestUser(VERIFIED_USER);
		} catch (error) {
			// If user already exists, that's fine for testing
			if (!(error instanceof Error) || !error.message?.includes('already exists')) {
				console.error('Failed to create verified test user:', error);
				throw error;
			}
		}
	});

	// Clean up test users after all tests complete
	test.afterAll(async () => {
		try {
			await deleteTestUser(UNVERIFIED_USER.email);
			await deleteTestUser(VERIFIED_USER.email);
		} catch (error) {
			console.error('Failed to cleanup verify test users:', error);
			// Don't throw to avoid failing the test run
		}
	});

	test.describe('When not signed in', () => {
		test('shows login prompt', async ({ page, goto }) => {
			await goto('/verify');
			await waitForPageLoad(page);

			await expect(page.getByText('Please Log In')).toBeVisible();
			await expect(page.getByText('Log in to access your account and verify your email.')).toBeVisible();
			await expect(page.getByRole('link', { name: 'Log In' })).toBeVisible();
		});

		test('shows error message for invalid token when not signed in', async ({ page, goto }) => {
			await goto('/verify?error=invalid_token');
			await waitForPageLoad(page);

			await expect(page.getByText('There Was A Problem')).toBeVisible();
			await expect(page.getByText('The verification link is invalid or expired. Please log in to resend the verification email.')).toBeVisible();
		});

		test('navigates to sign-in page when clicking login button', async ({ page, goto }) => {
			await goto('/verify');
			await waitForPageLoad(page);

			await page.getByRole('link', { name: 'Log In' }).click();
			await expect(page).toHaveURL('/sign-in');
		});
	});

	test.describe('When signed in with unverified email', () => {
		test.beforeEach(async ({ page }) => {
			await signInUser(page, UNVERIFIED_USER.email, UNVERIFIED_USER.password);
		});

		test('shows resend verification interface', async ({ page, goto }) => {
			await goto('/verify');
			await waitForPageLoad(page);

			await expect(page.getByText('Please Verify Your Account')).toBeVisible();
			await expect(page.getByText('Please click the link we sent to you when you signed up or click below to resend.')).toBeVisible();
			await expect(page.getByRole('button', { name: 'Resend Verification Email' })).toBeVisible();
		});

		test('allows resending verification email', async ({ page, goto }) => {
			await goto('/verify');
			await waitForPageLoad(page);

			const resendButton = page.getByRole('button', { name: 'Resend Verification Email' });
			await expect(resendButton).toBeVisible();

			await resendButton.click();

			// Should show loading state briefly, then success state
			await expect(page.getByRole('heading', { name: 'Verification Sent' })).toBeVisible({ timeout: 10000 });
			await expect(page.getByText('We have sent the verification email. Check your inbox.')).toBeVisible();
			await expect(page.getByRole('button', { name: 'Verification Sent' })).toBeDisabled();
		});

		test('shows error state for invalid token', async ({ page, goto }) => {
			await goto('/verify?error=invalid_token');
			await waitForPageLoad(page);

			await expect(page.getByText('There Was A Problem')).toBeVisible();
			await expect(page.getByText('The verification link is invalid or expired. Please click below to resend the verification email.')).toBeVisible();
			await expect(page.getByRole('button', { name: 'Resend Verification Email' })).toBeVisible();
		});
	});

	test.describe('When signed in with verified email', () => {
		test.beforeEach(async ({ page }) => {
			await signInUser(page, VERIFIED_USER.email, VERIFIED_USER.password);

			// Manually verify the user in the database for this test
			// This ensures the user is actually verified when we test
			await db.update(user).set({ emailVerified: true }).where(eq(user.email, VERIFIED_USER.email));
		});

		test('shows verification success message', async ({ page, goto }) => {
			await goto('/verify');
			await waitForPageLoad(page);

			await expect(page.getByText('Email Verified')).toBeVisible();
			await expect(page.getByText('Your email was successfully verified. Please click below to continue.')).toBeVisible();
			await expect(page.getByRole('link', { name: 'Continue' })).toBeVisible();
		});

		test('navigates to home page when clicking continue', async ({ page, goto }) => {
			await goto('/verify');
			await waitForPageLoad(page);

			await page.getByRole('link', { name: 'Continue' }).click();
			await expect(page).toHaveURL('/');
		});
	});

	test.describe('Error handling', () => {
		test.beforeEach(async ({ page }) => {
			await signInUser(page, UNVERIFIED_USER.email, UNVERIFIED_USER.password);
		});

		test('handles resend email errors gracefully', async ({ page, goto }) => {
			await goto('/verify');
			await waitForPageLoad(page);
		});
	});
});
